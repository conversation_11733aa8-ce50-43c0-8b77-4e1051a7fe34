// Survey Screen Styles
.survey-screen {
  .selected-zone {
    max-width: 700px;
  }

  // Loading Dialog Styles
  .loading-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
  }

  .loading-dialog {
    border-radius: 16px;
    padding: 12px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    animation: fadeIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .loading-content {
    text-align: center;
    color: #ffffff;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 20px;
  }

  .loading-title {
    height: 76px;
    font-weight: 900;
    font-style: italic;
    font-size: 22px;
    line-height: 32px;
    letter-spacing: 0;
    text-align: center;
    margin: 0 0 12px 0;
  }

  .loading-subtitle {
    height: 24;
    font-weight: 700;
    font-style: italic;
    font-size: 14px;
    line-height: 120%;
    letter-spacing: 0;
    text-align: center;
    margin: 0 0 8px 0;
  }

  // Notebook container styles
  .notebook-container {
    position: relative;
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    width: 100%;
    max-width: 400px;
    
    img {
      max-width: 100%;
      height: auto;
    }
  }

  .loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #ffffff;
    animation: spin 1s linear infinite;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }


  // Header Section Styles
  .survey-header-section {
    text-align: center;

    .survey-line1 {
      font-size: 20px;
      font-style: italic;
      font-weight: 700;
      line-height: normal;
      text-align: center;
      margin: 0 0 8px 0;
    }

    .survey-line2 {
      font-size: 28px;
      font-style: italic;
      font-weight: 700;
      line-height: normal;
      text-align: center;
      margin: 0 0 12px 0;
    }

    .survey-line3 {
      font-size: 12px;
      font-style: italic;
      font-weight: 700;
      line-height: normal;
      text-align: center;
      margin: 0;

      .number-highlight {
        font-size: 32px;
        leading-trim: both;
        text-edge: cap;
        vertical-align: middle;
      }
    }
  }

  
  .question-note-text {
    color: #492405;
    font-size: 11px;
    font-style: bold;
    font-weight: 800;
    line-height: 14px;
    margin: 0 0 var(--spacing-md) 0;
  }

  // Survey Body Section
  .survey-body-section {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 40px 8px;
    min-height: 400px;
  }

  // Survey Question Item
  .survey-question-item {
    margin-left: 6px;
    margin-right: 6px;
    text-align: left; // Ensure all content is left-aligned
    position: relative; // Enable positioning context for children

    .question-number-box {
      display: inline-block;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      padding: 8px 24px;
      position: relative;
      z-index: 2;
      transform: translateY(16px); // Move down to overlap
      text-align: left;

      .question-number-text {
        color: #F0EBE3;
        text-align: center;
        font-size: 14px;
        font-style: italic;
        font-weight: 700;
        line-height: 16px;
      }
    }

    .question-text-box {
      position: relative; // Enable positioning for text overlay
      margin-bottom: var(--spacing-sm);
      z-index: 1; // Place below question number box

      .question-bg-image {
        width: 100%;
        height: auto;
        display: block;
        object-fit: contain; // Maintain aspect ratio
      }

      .question-main-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 90%; // Leave some padding on sides
        text-align: start;
        color: #0C1BAC;
        font-size: 11px;
        font-style: italic;
        font-weight: 700;
        line-height: 14px;
        margin: 0;
        padding: var(--spacing-md) 4px;
      }
    }


    .answer-options-container {
      text-align: left;
      
      .option-item {
        margin-bottom: var(--spacing-sm);
        overflow: visible; // Allow checkbox SVG to overflow
        text-align: left;

        .zaui-radio {
          width: 100%;
          
          // Style the radio button indicator
          .zaui-radio__input {
            width: 14px;
            height: 14px;
            border-radius: 2px; // Square shape with slight rounding
            
            &::before {
              width: 14px;
              height: 14px;
              border-radius: 2px; // Square shape
            }
            
            &::after {
              width: 8px;
              height: 8px;
              border-radius: 1px; // Square inner indicator
              top: 3px;
              left: 3px;
            }
          }
          
          .zaui-radio__label {
            color: #492405;
            leading-trim: both;
            text-edge: cap;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
          }
        }
      }

      .survey-radio-option {
        // Style the radio button indicator
        .zaui-radio__input {
          width: 14px;
          height: 14px;
          border-radius: 2px; // Square shape with slight rounding
          
          &::before {
            width: 14px;
            height: 14px;
            border-radius: 2px; // Square shape
          }
          
          &::after {
            width: 8px;
            height: 8px;
            border-radius: 1px; // Square inner indicator
            top: 3px;
            left: 3px;
          }
        }
        
        .zaui-radio__label {
          color: #492405;
          leading-trim: both;
          text-edge: cap;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 16px;
        }
      }
      
      // Custom checkbox styles
      .custom-checkbox-container {
        display: flex;
        align-items: center;
        cursor: pointer;
        user-select: none;
        
        &:hover .custom-checkbox {
          border-color: #9A938E;
        }
        
        .custom-checkbox {
          width: 18px;
          height: 18px;
          border: 1px solid #B6AEA9;
          border-radius: 3.5px;
          background: transparent;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          transition: all 0.2s ease;
          overflow: visible; // Cho phép SVG hiển thị vượt ra ngoài
          position: relative;
          
          svg {
            width: 24px; // Fixed width
            height: 17px; // Fixed height
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%); // Center the SVG
            z-index: 1; // Ensure SVG appears above the checkbox border
          }
        }
        
        .custom-checkbox-label {
          color: #492405;
          leading-trim: both;
          text-edge: cap;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 16px;
          margin-left: 8px;
          text-align: left;
        }
      }
    }
  }

  // Style for yes-no questions
  .zaui-radio-group {
    .zaui-radio {
      // Style the radio button indicator for yes-no questions
      .zaui-radio__input {
        width: 14px;
        height: 14px;
        border-radius: 2px; // Square shape with slight rounding
        
        &::before {
          width: 14px;
          height: 14px;
          border-radius: 2px; // Square shape
        }
        
        &::after {
          width: 8px;
          height: 8px;
          border-radius: 1px; // Square inner indicator
          top: 3px;
          left: 3px;
        }
      }
    }
  }

  // Keep existing styles for other question types
  .rating-container {
    .rating-buttons {
      display: flex;
      gap: var(--spacing-sm);
      margin-bottom: var(--spacing-md);

      .rating-button {
        flex: 1;
        aspect-ratio: 1;
        border-radius: 50%;
      }
    }

    .rating-labels {
      display: flex;
      justify-content: space-between;
      font-size: var(--font-size-sm);
      color: var(--gray);
    }
  }

  .slider-container {
    .slider-labels {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: var(--spacing-md);

      .slider-value {
        background: var(--primary-color);
        color: var(--white);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius-sm);
        font-weight: bold;
      }
    }
  }
}