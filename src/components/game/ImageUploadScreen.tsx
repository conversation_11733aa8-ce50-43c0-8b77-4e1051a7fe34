import React, { useState, useRef } from 'react';
import { Box, Page, useNavigate } from 'zmp-ui';
import { v4 as uuidv4 } from 'uuid';
import GameHeader from './GameHeader';
import selectButtonImage from '@/static/select-button.png';
import uploadPicImage from '@/static/upload/upload-pic.png';
import flowerImage from '@/static/flower.png';
import logoBrandImage from '@/static/logo-brand.png';
import { trackScreenView, trackImageUpload, trackError, sendGA4Event } from '@/utils/ga4-tracking';
import { uploadImageInBackground } from '@/utils/api';
import flowerImageDecor from '@/static/upload/flower-decor.png';

interface ImageUploadScreenProps {
  onImageUpload: (images: File[]) => void;
  uploadedImages: File[];
}

const ImageUploadScreen: React.FC<ImageUploadScreenProps> = ({
  onImageUpload,
  uploadedImages
}) => {
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [userName, setUserName] = useState('');
  const [uploading, setUploading] = useState(false);
  const [imageOrientation, setImageOrientation] = useState<'portrait' | 'landscape' | null>(null);

  React.useEffect(() => {
    // Track screen view when component mounts
    trackScreenView('ImageUploadScreen', 'Game');
  }, []);

  const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxFileSize = 10 * 1024 * 1024; // 10MB
  const maxFiles = 5;

  const validateFile = (file: File): string | null => {
    if (!supportedFormats.includes(file.type)) {
      return 'Unsupported file format. Please use JPG, PNG, GIF, or WebP.';
    }
    if (file.size > maxFileSize) {
      return 'File size too large. Maximum size is 10MB.';
    }
    return null;
  };

  const handleFiles = async (files: FileList) => {
    setUploading(true);
    const validFiles: File[] = [];
    const errors: string[] = [];

    // Since we only want one image, just check the first file
    if (files.length > 0) {
      const file = files[0];
      
      // Check if file is a valid File object
      if (!file || !(file instanceof File)) {
        errors.push(`Invalid file object`);
      } else {
        const error = validateFile(file);
        if (error) {
          errors.push(`${file.name}: ${error}`);
        } else {
          validFiles.push(file);
        }
      }
    }

    if (errors.length > 0) {
      // Show error toast or modal
      console.error('Upload errors:', errors);
      // Track upload errors
      await trackError('image_upload_error', errors.join('; '), 'ImageUploadScreen');
    }

    if (validFiles.length > 0) {
      // Replace existing image with new one (not append)
      onImageUpload(validFiles);
      
      // Reset orientation for new image
      setImageOrientation(null);
      
      // Track successful image upload
      const sessionId = localStorage.getItem('gameState') ? 
        JSON.parse(localStorage.getItem('gameState') || '{}').sessionId : 
        'unknown';
      const totalSize = validFiles[0].size;
      await trackImageUpload(sessionId, 1, totalSize);
    }

    // Reset file input to allow re-selection of the same file
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    setUploading(false);
  };

  const handleFileSelect = async () => {
    await sendGA4Event({
      name: 'image_select_clicked',
      params: {
        screen_name: 'ImageUploadScreen',
        button_location: 'upload_area',
        current_image_count: uploadedImages.length
      }
    });
    fileInputRef.current?.click();
  };

  const uploadImageToAPI = (): void => {
    // Get session ID from localStorage
    const gameState = localStorage.getItem('gameState');
    const sessionId = gameState ? JSON.parse(gameState).sessionId : uuidv4();
    
    // Store user info in localStorage for later use
    const currentGameState = JSON.parse(localStorage.getItem('gameState') || '{}');
    currentGameState.userId = sessionId;
    currentGameState.userName = userName.trim();
    localStorage.setItem('gameState', JSON.stringify(currentGameState));
    
    // Use centralized API function
    uploadImageInBackground(userName.trim(), sessionId, uploadedImages[0]);
    
    // Immediately navigate to survey without waiting
    navigate('/survey');
  };

  const handleNext = () => {
    if (uploadedImages.length > 0 && userName.trim()) {
      sendGA4Event({
        name: 'image_upload_submitted',
        params: {
          screen_name: 'ImageUploadScreen',
          button_location: 'footer',
          image_count: uploadedImages.length,
          user_name: userName || 'not_provided'
        }
      });
      
      // Call the upload API (fire-and-forget)
      uploadImageToAPI();
    }
  };

  const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const img = e.currentTarget;
    const orientation = img.naturalWidth > img.naturalHeight ? 'landscape' : 'portrait';
    setImageOrientation(orientation);
    
    // Clean up the object URL after the image loads
    URL.revokeObjectURL(img.src);
  };

  return (
    <Page className="image-upload-screen">
      {/* Header */}
      <GameHeader />
    
      {/* Main Content Area */}
      <Box className="main-content" style={{ position: 'relative' }}>
        <img
          src={flowerImageDecor}
          alt="Ensure Logo"
          style={{ position: 'absolute', top: '88px', left: 0 }}
          className="h-44"
        />
        <Box className='hero-section'>
                    {/* Header Section with Title and Name Input */}
        <Box className="upload-header-section">
          <h2 className="gradient-text upload-title">
            Phiếu Bé Ngoan này của:
          </h2>
          
          <label className="name-input-label">
            Nhập tên của bạn (Ví dụ: Huyền Trang)
          </label>
          
          <Box className="name-input-container" style={{ position: 'relative' }}>
            <span 
              className="dotted-placeholder"
              style={{ opacity: userName ? 0 : 1 }}
            >
              ..........................................................................
            </span>
            <input
              type="text"
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
              className="name-input"
              maxLength={12}
              required
            />
            <span 
              style={{
                position: 'absolute',
                bottom: '-20px',
                right: '0',
                fontSize: '12px',
                color: '#666',
                fontWeight: 'normal'
              }}
            >
              ({userName.length}/12)
            </span>
          </Box>
        </Box>

        <img src={flowerImage} alt="Flower" className="flower-image" />


        {/* Photo Section with Frame and Upload Area */}
        <Box className="photo-upload-section">
          <Box 
            onClick={handleFileSelect}
            className="upload-frame-container"
          >
            <img 
              src={uploadPicImage} 
              alt="Upload frame"
              className="upload-frame"
            />
            
            {/* Display uploaded image */}
            {uploadedImages.length > 0 && uploadedImages[0] && (
              <Box className="uploaded-image-container">
                <img
                  src={URL.createObjectURL(uploadedImages[0])}
                  alt="Uploaded"
                  className={`uploaded-image ${imageOrientation || ''}`}
                  onLoad={handleImageLoad}
                />
              </Box>
            )}
          </Box>
          
          <input
            ref={fileInputRef}
            type="file"
            accept={supportedFormats.join(',')}
            onChange={(e) => e.target.files && handleFiles(e.target.files)}
            style={{ display: 'none' }}
          />
        </Box>

        {/* Instructions Section */}
        <Box className="instructions-section">
          <p className="gradient-text instructions-text">
            Tải hình ảnh của bạn<br />
            chụp với Cha Mẹ
          </p>
        </Box>
        </Box>

      </Box>

      {/* Selected Zone (Footer) - Only Action Buttons */}
      <Box className="selected-zone">
        <Box className="action-buttons">
          <button 
            className="image-button" 
            onClick={handleNext}
            disabled={uploadedImages.length === 0 || !userName.trim()}
          >
            <img src={selectButtonImage} alt="Continue" />
            <span className="button-text gradient-text">TIẾP TỤC</span>
          </button>
        </Box>
        
        {/* Brand Logo */}
        <img 
          src={logoBrandImage} 
          alt="Brand Logo" 
          style={{
            position: 'absolute',
            bottom: '15px',
            right: '15px',
            width: 'auto',
            height: '20px',
            zIndex: 10
          }}
        />
      </Box>
    </Page>
  );
};

export default ImageUploadScreen;
